using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.EditorInput;
using AcApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace RoadMarkingPlugin
{
    public class PluginCommands
    {
        private static RoadMarkingForm roadMarkingForm = null;

        /// <summary>
        /// 道路标线插件主命令
        /// </summary>
        [CommandMethod("ROADMARKING", CommandFlags.Modal)]
        public void ShowRoadMarkingForm()
        {
            try
            {
                Document doc = AcApp.DocumentManager.MdiActiveDocument;
                if (doc == null)
                {
                    Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                    ed?.WriteMessage("\n没有活动的AutoCAD文档！");
                    return;
                }

                // 如果窗体不存在或已被释放，创建新的窗体
                if (roadMarkingForm == null || roadMarkingForm.IsDisposed)
                {
                    roadMarkingForm = new RoadMarkingForm();
                }

                // 显示窗体
                if (roadMarkingForm.Visible)
                {
                    roadMarkingForm.BringToFront();
                    roadMarkingForm.Focus();
                }
                else
                {
                    roadMarkingForm.Show();
                }
            }
            catch (System.Exception ex)
            {
                Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage($"\n启动道路标线插件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 插件初始化
        /// </summary>
        [CommandMethod("ROADMARKING_INIT", CommandFlags.Modal)]
        public void InitializePlugin()
        {
            try
            {
                Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage("\n道路标线插件已初始化");
                ed?.WriteMessage("\n使用命令 ROADMARKING 打开道路标线工具");
            }
            catch (System.Exception ex)
            {
                Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage($"\n插件初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 插件信息
        /// </summary>
        [CommandMethod("ROADMARKING_INFO", CommandFlags.Modal)]
        public void ShowPluginInfo()
        {
            try
            {
                Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage("\n=== 道路标线插件信息 ===");
                ed?.WriteMessage("\n插件名称: 道路标线生成工具");
                ed?.WriteMessage("\n版本: 1.0");
                ed?.WriteMessage("\n功能: 自动生成道路标线、停止线、斑马线等");
                ed?.WriteMessage("\n命令: ROADMARKING - 打开主界面");
                ed?.WriteMessage("\n========================");
            }
            catch (System.Exception ex)
            {
                Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage($"\n显示插件信息失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 插件应用程序类
    /// </summary>
    public class PluginApplication : IExtensionApplication
    {
        public void Initialize()
        {
            try
            {
                Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage("\n道路标线插件已加载");
                ed?.WriteMessage("\n使用命令 ROADMARKING 打开道路标线工具");
            }
            catch (System.Exception ex)
            {
                Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage($"\n插件加载失败: {ex.Message}");
            }
        }

        public void Terminate()
        {
            try
            {
                Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage("\n道路标线插件已卸载");
            }
            catch (System.Exception ex)
            {
                Editor ed = AcApp.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage($"\n插件卸载失败: {ex.Message}");
            }
        }
    }
}
