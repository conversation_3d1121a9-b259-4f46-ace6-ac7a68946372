// 正确的起始线偏移方式 - 代码模板
// =====================================
// 
// 这是经过验证的正确偏移代码，能够生成跟随原始基准线形状的偏移线条
// 当需要"采用正确的起始线偏移方式"时，使用这个代码模板
//
// 核心原理：
// 1. 直接使用AutoCAD的GetOffsetCurves方法
// 2. 立即将偏移结果添加到图形中
// 3. 正确设置颜色和清理多余结果
// 4. 适用于所有几何类型：直线、样条曲线、圆弧、多段线

using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;

namespace RoadMarkingPlugin
{
    /// <summary>
    /// 正确的起始线偏移方式
    /// </summary>
    public static class CorrectOffsetMethod
    {
        /// <summary>
        /// 执行双侧偏移操作（左偏移和右偏移）
        /// </summary>
        /// <param name="startLine">原始基准线</param>
        /// <param name="offsetDistance">偏移距离（正值）</param>
        /// <param name="millingColor">偏移线条颜色</param>
        /// <param name="btr">块表记录</param>
        /// <param name="trans">事务</param>
        /// <param name="editor">编辑器</param>
        /// <returns>返回左偏移线条和右偏移线条</returns>
        public static (Curve leftOffsetLine, Curve rightOffsetLine) CreateDualOffset(
            Curve startLine, double offsetDistance, short millingColor,
            BlockTableRecord btr, Transaction trans, Editor editor)
        {
            // 计算偏移值：左偏移为负值，右偏移为正值
            double leftOffsetValue = -offsetDistance;   // 左偏移为负值
            double rightOffsetValue = offsetDistance;    // 右偏移为正值
            
            editor.WriteMessage($"\n使用正确的偏移方式: 左={leftOffsetValue:F3}mm, 右={rightOffsetValue:F3}mm");
            
            // 执行左偏移
            Curve leftOffsetLine = CreateSingleOffset(startLine, leftOffsetValue, millingColor, btr, trans, editor, "左");
            
            // 执行右偏移
            Curve rightOffsetLine = CreateSingleOffset(startLine, rightOffsetValue, millingColor, btr, trans, editor, "右");
            
            return (leftOffsetLine, rightOffsetLine);
        }
        
        /// <summary>
        /// 执行单侧偏移操作
        /// </summary>
        /// <param name="startLine">原始基准线</param>
        /// <param name="offsetValue">偏移值（负值为左偏移，正值为右偏移）</param>
        /// <param name="millingColor">偏移线条颜色</param>
        /// <param name="btr">块表记录</param>
        /// <param name="trans">事务</param>
        /// <param name="editor">编辑器</param>
        /// <param name="sideName">偏移方向名称（用于调试输出）</param>
        /// <returns>偏移后的曲线</returns>
        public static Curve CreateSingleOffset(
            Curve startLine, double offsetValue, short millingColor,
            BlockTableRecord btr, Transaction trans, Editor editor, string sideName)
        {
            try
            {
                editor.WriteMessage($"\n执行{sideName}偏移，距离: {offsetValue:F3}mm");
                
                // 步骤1：使用AutoCAD的GetOffsetCurves方法进行偏移
                DBObjectCollection offsetCurves = startLine.GetOffsetCurves(offsetValue);
                
                if (offsetCurves == null || offsetCurves.Count == 0)
                {
                    editor.WriteMessage($"\n{sideName}偏移操作失败：无法生成偏移曲线");
                    return null;
                }
                
                // 步骤2：获取第一个偏移结果（通常只有一个）
                Curve offsetLine = (Curve)offsetCurves[0];
                
                // 步骤3：设置偏移线条的颜色
                offsetLine.ColorIndex = millingColor;
                
                // 步骤4：将偏移线条添加到图形中
                ObjectId offsetId = btr.AppendEntity(offsetLine);
                trans.AddNewlyCreatedDBObject(offsetLine, true);
                
                // 步骤5：清理其他偏移结果（如果有多个）
                for (int i = 1; i < offsetCurves.Count; i++)
                {
                    offsetCurves[i].Dispose();
                }
                
                // 步骤6：输出成功信息
                editor.WriteMessage($"\n  {sideName}偏移成功: {startLine.GetType().Name} -> {offsetLine.GetType().Name}");
                editor.WriteMessage($"\n  {sideName}偏移线条已添加到图形，ObjectId: {offsetId}");
                
                return offsetLine;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n{sideName}偏移操作异常: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 仅创建偏移曲线，不添加到图形（用于后续处理）
        /// </summary>
        /// <param name="startLine">原始基准线</param>
        /// <param name="offsetValue">偏移值</param>
        /// <param name="millingColor">偏移线条颜色</param>
        /// <param name="editor">编辑器</param>
        /// <param name="sideName">偏移方向名称</param>
        /// <returns>偏移后的曲线（未添加到图形）</returns>
        public static Curve CreateOffsetCurveOnly(
            Curve startLine, double offsetValue, short millingColor, Editor editor, string sideName = "")
        {
            try
            {
                editor.WriteMessage($"\n创建{sideName}偏移曲线，距离: {offsetValue:F3}mm");
                
                // 使用AutoCAD的GetOffsetCurves方法进行偏移
                DBObjectCollection offsetCurves = startLine.GetOffsetCurves(offsetValue);
                
                if (offsetCurves == null || offsetCurves.Count == 0)
                {
                    editor.WriteMessage($"\n{sideName}偏移操作失败：无法生成偏移曲线");
                    return null;
                }
                
                // 获取第一个偏移结果
                Curve offsetLine = (Curve)offsetCurves[0];
                offsetLine.ColorIndex = millingColor;
                
                // 清理其他偏移结果
                for (int i = 1; i < offsetCurves.Count; i++)
                {
                    offsetCurves[i].Dispose();
                }
                
                editor.WriteMessage($"\n  {sideName}偏移曲线创建成功: {startLine.GetType().Name} -> {offsetLine.GetType().Name}");
                
                return offsetLine;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n{sideName}偏移曲线创建异常: {ex.Message}");
                return null;
            }
        }
    }
}

/*
使用示例：

// 示例1：双侧偏移并立即添加到图形
var (leftLine, rightLine) = CorrectOffsetMethod.CreateDualOffset(
    startLine, offsetDistance, millingColor, btr, trans, editor);

// 示例2：单侧偏移并立即添加到图形
Curve leftOffsetLine = CorrectOffsetMethod.CreateSingleOffset(
    startLine, -offsetDistance, millingColor, btr, trans, editor, "左");

// 示例3：仅创建偏移曲线，不添加到图形（用于后续处理）
Curve offsetCurve = CorrectOffsetMethod.CreateOffsetCurveOnly(
    startLine, offsetDistance, millingColor, editor, "左");

核心要点：
1. 使用GetOffsetCurves方法是正确的OFFSET命令实现
2. 负值表示左偏移，正值表示右偏移
3. 立即添加到图形可以确保偏移结果正确显示
4. 必须清理多余的偏移结果以避免内存泄漏
5. 适用于所有几何类型：Line、Spline、Arc、Polyline
*/
