# 停止线移动操作说明

## 移动操作概述

在生成停止线矩形后，对停止线进行移动调整，以优化停止线的最终位置。

### 移动要求
- **起始点附近的停止线**：沿停止线长边方向向左移动
- **结束点附近的停止线**：沿停止线长边方向向右移动
- **移动距离**：车道虚线宽度 × 0.5

## 实现流程

### 第二步：起始端停止线（增加移动操作）
```csharp
// 5. 使用offset偏移命令创建矩形停止线
CreateStopLineWithOffset(stopLineStart, stopLineEnd, stopLineWidth, roadDirection, 
                       true, currentColor, entitiesToAdd, editor);

// 6. 对生成的停止线进行移动 - 起始点附近的停止线向左移动
MoveStopLine(entitiesToAdd, stopLineStart, stopLineEnd, true, editor);
```

### 第三步：结束端停止线（增加移动操作）
```csharp
// 5. 使用offset偏移命令创建矩形停止线
CreateStopLineWithOffset(stopLineStart, stopLineEnd, stopLineWidth, roadDirection, 
                       false, currentColor, entitiesToAdd, editor);

// 6. 对生成的停止线进行移动 - 结束点附近的停止线向右移动
MoveStopLine(entitiesToAdd, stopLineStart, stopLineEnd, false, editor);
```

## 移动操作详细实现

### MoveStopLine方法实现
```csharp
private void MoveStopLine(List<Entity> entitiesToAdd, Point3d stopLineStart, Point3d stopLineEnd, 
                        bool isStartEnd, Editor editor)
{
    // 1. 获取车道虚线宽度参数
    double dashLineWidth = GetDoubleValue(this.textBox8, 0.15); // 车道虚线宽度
    double moveDistance = dashLineWidth * 0.5; // 移动距离=车道虚线宽度*0.5
    
    // 2. 计算停止线长边方向
    Vector3d stopLineDirection = (stopLineEnd - stopLineStart).GetNormal();
    
    // 3. 确定移动方向
    Vector3d moveDirection;
    if (isStartEnd)
    {
        // 起始点附近的停止线向左移动
        moveDirection = -stopLineDirection;
    }
    else
    {
        // 结束点附近的停止线向右移动
        moveDirection = stopLineDirection;
    }
    
    // 4. 计算移动向量
    Vector3d moveVector = moveDirection * moveDistance;
    
    // 5. 获取最近添加的停止线实体（最后4个实体是刚创建的停止线矩形）
    int stopLineEntityCount = 4; // 停止线矩形包含4条边
    int startIndex = Math.Max(0, entitiesToAdd.Count - stopLineEntityCount);
    
    // 6. 对停止线的所有实体进行移动
    for (int i = startIndex; i < entitiesToAdd.Count; i++)
    {
        if (entitiesToAdd[i] is Line line)
        {
            // 移动线段的起点和终点
            Point3d newStartPoint = line.StartPoint + moveVector;
            Point3d newEndPoint = line.EndPoint + moveVector;
            
            // 创建新的移动后的线段
            Line movedLine = new Line(newStartPoint, newEndPoint);
            movedLine.ColorIndex = line.ColorIndex;
            
            // 替换原来的线段
            entitiesToAdd[i] = movedLine;
        }
    }
}
```

## 移动方向说明

### 起始端停止线移动
- **停止线长边方向**：从stopLineStart到stopLineEnd的方向
- **移动方向**：向左移动，即-stopLineDirection
- **移动距离**：车道虚线宽度 × 0.5

### 结束端停止线移动
- **停止线长边方向**：从stopLineStart到stopLineEnd的方向
- **移动方向**：向右移动，即+stopLineDirection
- **移动距离**：车道虚线宽度 × 0.5

## 参数计算

### 移动距离计算
```csharp
double dashLineWidth = GetDoubleValue(this.textBox8, 0.15); // 从textBox8读取车道虚线宽度
double moveDistance = dashLineWidth * 0.5; // 移动距离=车道虚线宽度*0.5
```

### 移动向量计算
```csharp
// 停止线长边方向
Vector3d stopLineDirection = (stopLineEnd - stopLineStart).GetNormal();

// 移动方向
Vector3d moveDirection = isStartEnd ? -stopLineDirection : stopLineDirection;

// 移动向量
Vector3d moveVector = moveDirection * moveDistance;
```

## 实体移动处理

### 停止线矩形实体识别
- **停止线矩形**：包含4条边（Line实体）
- **实体位置**：在entitiesToAdd列表的最后4个位置
- **移动范围**：从startIndex到列表末尾的所有Line实体

### 实体移动操作
```csharp
// 对每个Line实体进行移动
Point3d newStartPoint = line.StartPoint + moveVector;
Point3d newEndPoint = line.EndPoint + moveVector;

// 创建移动后的新线段
Line movedLine = new Line(newStartPoint, newEndPoint);
movedLine.ColorIndex = line.ColorIndex;

// 替换原来的线段
entitiesToAdd[i] = movedLine;
```

## 调试信息

### 移动操作调试输出
```
=== 停止线移动操作 ===
位置: 起始端
车道虚线宽度: 0.150
移动距离: 0.075
停止线长边方向: (0.000, 1.000, 0.000)
起始端停止线向左移动
移动方向: (0.000, -1.000, 0.000)
移动向量: (0.000, -0.075, 0.000)
移动停止线实体，从索引 12 开始，共 4 个实体
  线段 12: (0.000, 0.000) -> (0.000, 6.550)
    移动后: (0.000, -0.075) -> (0.000, 6.475)
  线段 13: (0.000, 6.550) -> (-0.200, 6.550)
    移动后: (0.000, 6.475) -> (-0.200, 6.475)
  线段 14: (-0.200, 6.550) -> (-0.200, 0.000)
    移动后: (-0.200, 6.475) -> (-0.200, -0.075)
  线段 15: (-0.200, 0.000) -> (0.000, 0.000)
    移动后: (-0.200, -0.075) -> (0.000, -0.075)
停止线移动完成
```

## 移动效果

### ✅ 起始端停止线
- **原始位置**：位于左侧长虚线起始点
- **移动后位置**：沿停止线长边方向向左移动车道虚线宽度×0.5的距离
- **移动方向**：-stopLineDirection（向左）

### ✅ 结束端停止线
- **原始位置**：位于右侧长虚线结束点
- **移动后位置**：沿停止线长边方向向右移动车道虚线宽度×0.5的距离
- **移动方向**：+stopLineDirection（向右）

### ✅ 移动精度
- **移动距离**：精确到车道虚线宽度的一半
- **移动方向**：严格沿停止线长边方向
- **实体处理**：所有停止线矩形的4条边都同步移动

## 总结

通过添加移动操作，停止线的最终位置得到了精确调整：
1. **起始端停止线**：向左移动，优化与长虚线的位置关系
2. **结束端停止线**：向右移动，保持对称性
3. **移动距离**：车道虚线宽度×0.5，确保适当的偏移量
4. **移动方向**：沿停止线长边方向，保持停止线的垂直特性

现在停止线的位置更加精确，符合道路标线设计的细节要求！
