@echo off
chcp 65001 >nul
echo ========================================
echo 道路标线CAD插件编译脚本
echo ========================================

echo 开始编译项目...
echo.

dotnet build RoadMarkingPlugin.csproj --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 编译成功！
    echo ========================================
    echo.
    echo DLL文件位置：
    if exist "bin\Release\RoadMarkingPlugin.dll" (
        echo   bin\Release\RoadMarkingPlugin.dll
        echo.
        echo 文件信息：
        dir "bin\Release\RoadMarkingPlugin.dll"
    ) else (
        echo   未找到输出文件，请检查编译日志
    )
    echo.
    echo 安装说明：
    echo 1. 将RoadMarkingPlugin.dll复制到任意文件夹
    echo 2. 在AutoCAD中使用NETLOAD命令加载插件
    echo 3. 使用ROADMARKING命令打开道路标线工具
    echo.
    echo 兼容版本：AutoCAD 2021及以上版本
    echo.
) else (
    echo.
    echo ========================================
    echo 编译失败！错误代码：%ERRORLEVEL%
    echo ========================================
    echo.
    echo 请检查以下问题：
    echo 1. AutoCAD引用路径是否正确
    echo 2. .NET Framework版本是否匹配
    echo 3. 代码语法是否有错误
    echo.
)

pause
